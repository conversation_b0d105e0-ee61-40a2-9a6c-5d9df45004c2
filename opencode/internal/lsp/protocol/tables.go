package protocol

var TableKindMap = map[SymbolKind]string{
	File:          "File",
	Module:        "Module",
	Namespace:     "Namespace",
	Package:       "Package",
	Class:         "Class",
	Method:        "Method",
	Property:      "Property",
	Field:         "Field",
	Constructor:   "Constructor",
	Enum:          "Enum",
	Interface:     "Interface",
	Function:      "Function",
	Variable:      "Variable",
	Constant:      "Constant",
	String:        "String",
	Number:        "Number",
	Boolean:       "Boolean",
	Array:         "Array",
	Object:        "Object",
	Key:           "Key",
	Null:          "Null",
	EnumMember:    "EnumMember",
	Struct:        "Struct",
	Event:         "Event",
	Operator:      "Operator",
	TypeParameter: "TypeParameter",
}
