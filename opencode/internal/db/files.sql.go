// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: files.sql

package db

import (
	"context"
)

const createFile = `-- name: CreateFile :one
INSERT INTO files (
    id,
    session_id,
    path,
    content,
    version,
    created_at,
    updated_at
) VALUES (
    ?, ?, ?, ?, ?, strftime('%s', 'now'), strftime('%s', 'now')
)
RETURNING id, session_id, path, content, version, created_at, updated_at
`

type CreateFileParams struct {
	ID        string `json:"id"`
	SessionID string `json:"session_id"`
	Path      string `json:"path"`
	Content   string `json:"content"`
	Version   string `json:"version"`
}

func (q *Queries) CreateFile(ctx context.Context, arg CreateFileParams) (File, error) {
	row := q.queryRow(ctx, q.createFileStmt, createFile,
		arg.ID,
		arg.SessionID,
		arg.Path,
		arg.Content,
		arg.Version,
	)
	var i File
	err := row.Scan(
		&i.ID,
		&i.SessionID,
		&i.Path,
		&i.Content,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteFile = `-- name: DeleteFile :exec
DELETE FROM files
WHERE id = ?
`

func (q *Queries) DeleteFile(ctx context.Context, id string) error {
	_, err := q.exec(ctx, q.deleteFileStmt, deleteFile, id)
	return err
}

const deleteSessionFiles = `-- name: DeleteSessionFiles :exec
DELETE FROM files
WHERE session_id = ?
`

func (q *Queries) DeleteSessionFiles(ctx context.Context, sessionID string) error {
	_, err := q.exec(ctx, q.deleteSessionFilesStmt, deleteSessionFiles, sessionID)
	return err
}

const getFile = `-- name: GetFile :one
SELECT id, session_id, path, content, version, created_at, updated_at
FROM files
WHERE id = ? LIMIT 1
`

func (q *Queries) GetFile(ctx context.Context, id string) (File, error) {
	row := q.queryRow(ctx, q.getFileStmt, getFile, id)
	var i File
	err := row.Scan(
		&i.ID,
		&i.SessionID,
		&i.Path,
		&i.Content,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getFileByPathAndSession = `-- name: GetFileByPathAndSession :one
SELECT id, session_id, path, content, version, created_at, updated_at
FROM files
WHERE path = ? AND session_id = ?
ORDER BY created_at DESC
LIMIT 1
`

type GetFileByPathAndSessionParams struct {
	Path      string `json:"path"`
	SessionID string `json:"session_id"`
}

func (q *Queries) GetFileByPathAndSession(ctx context.Context, arg GetFileByPathAndSessionParams) (File, error) {
	row := q.queryRow(ctx, q.getFileByPathAndSessionStmt, getFileByPathAndSession, arg.Path, arg.SessionID)
	var i File
	err := row.Scan(
		&i.ID,
		&i.SessionID,
		&i.Path,
		&i.Content,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const listFilesByPath = `-- name: ListFilesByPath :many
SELECT id, session_id, path, content, version, created_at, updated_at
FROM files
WHERE path = ?
ORDER BY created_at DESC
`

func (q *Queries) ListFilesByPath(ctx context.Context, path string) ([]File, error) {
	rows, err := q.query(ctx, q.listFilesByPathStmt, listFilesByPath, path)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []File{}
	for rows.Next() {
		var i File
		if err := rows.Scan(
			&i.ID,
			&i.SessionID,
			&i.Path,
			&i.Content,
			&i.Version,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listFilesBySession = `-- name: ListFilesBySession :many
SELECT id, session_id, path, content, version, created_at, updated_at
FROM files
WHERE session_id = ?
ORDER BY created_at ASC
`

func (q *Queries) ListFilesBySession(ctx context.Context, sessionID string) ([]File, error) {
	rows, err := q.query(ctx, q.listFilesBySessionStmt, listFilesBySession, sessionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []File{}
	for rows.Next() {
		var i File
		if err := rows.Scan(
			&i.ID,
			&i.SessionID,
			&i.Path,
			&i.Content,
			&i.Version,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listLatestSessionFiles = `-- name: ListLatestSessionFiles :many
SELECT f.id, f.session_id, f.path, f.content, f.version, f.created_at, f.updated_at
FROM files f
INNER JOIN (
    SELECT path, MAX(created_at) as max_created_at
    FROM files
    GROUP BY path
) latest ON f.path = latest.path AND f.created_at = latest.max_created_at
WHERE f.session_id = ?
ORDER BY f.path
`

func (q *Queries) ListLatestSessionFiles(ctx context.Context, sessionID string) ([]File, error) {
	rows, err := q.query(ctx, q.listLatestSessionFilesStmt, listLatestSessionFiles, sessionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []File{}
	for rows.Next() {
		var i File
		if err := rows.Scan(
			&i.ID,
			&i.SessionID,
			&i.Path,
			&i.Content,
			&i.Version,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listNewFiles = `-- name: ListNewFiles :many
SELECT id, session_id, path, content, version, created_at, updated_at
FROM files
WHERE is_new = 1
ORDER BY created_at DESC
`

func (q *Queries) ListNewFiles(ctx context.Context) ([]File, error) {
	rows, err := q.query(ctx, q.listNewFilesStmt, listNewFiles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []File{}
	for rows.Next() {
		var i File
		if err := rows.Scan(
			&i.ID,
			&i.SessionID,
			&i.Path,
			&i.Content,
			&i.Version,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateFile = `-- name: UpdateFile :one
UPDATE files
SET
    content = ?,
    version = ?,
    updated_at = strftime('%s', 'now')
WHERE id = ?
RETURNING id, session_id, path, content, version, created_at, updated_at
`

type UpdateFileParams struct {
	Content string `json:"content"`
	Version string `json:"version"`
	ID      string `json:"id"`
}

func (q *Queries) UpdateFile(ctx context.Context, arg UpdateFileParams) (File, error) {
	row := q.queryRow(ctx, q.updateFileStmt, updateFile, arg.Content, arg.Version, arg.ID)
	var i File
	err := row.Scan(
		&i.ID,
		&i.SessionID,
		&i.Path,
		&i.Content,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
