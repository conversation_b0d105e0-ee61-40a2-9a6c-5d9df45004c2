{"data": {}, "tui": {"theme": "opencode"}, "shell": {}, "providers": {"openai": {"apiKey": "your-openai-api-key-here", "disabled": false}, "anthropic": {"apiKey": "your-anthropic-api-key-here", "disabled": false}, "gemini": {"apiKey": "your-gemini-api-key-here", "disabled": false}, "groq": {"apiKey": "your-groq-api-key-here", "disabled": false}, "openrouter": {"apiKey": "your-openrouter-api-key-here", "disabled": false}}, "agents": {"coder": {"model": "claude-3.7-sonnet", "maxTokens": 5000}, "summarizer": {"model": "claude-3.7-sonnet", "maxTokens": 5000}, "task": {"model": "claude-3.7-sonnet", "maxTokens": 5000}, "title": {"model": "claude-3.7-sonnet", "maxTokens": 80}}}